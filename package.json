{"name": "my-next-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fontsource/montserrat": "^5.1.1", "@fontsource/poppins": "^5.1.1", "@fontsource/roboto": "^5.1.1", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@next-auth/mongodb-adapter": "^1.1.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-separator": "^1.1.2", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "aos": "^2.3.4", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "bcryptjs": "^3.0.0", "class-variance-authority": "^0.7.1", "formidable": "^3.5.2", "framer-motion": "^12.0.11", "lucide-react": "^0.474.0", "mongodb": "^5.9.2", "mongoose": "^8.10.1", "next": "^15.1.7", "next-auth": "^4.24.11", "nodemailer": "^6.10.0", "razorpay": "^2.9.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.4.0", "react-intersection-observer": "^9.15.1", "react-modal": "^3.16.3", "react-router-dom": "^7.1.5", "react-slick": "^0.30.3", "react-toastify": "^11.0.3", "recharts": "^2.15.1", "slick-carousel": "^1.8.1", "sonner": "^1.7.4", "tailwind-merge": "^3.0.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9.20.1", "eslint-config-next": "15.1.6", "postcss": "^8", "shadcn-ui": "^0.9.4", "tailwindcss": "^3.4.1", "typescript": "^5.7.3"}}