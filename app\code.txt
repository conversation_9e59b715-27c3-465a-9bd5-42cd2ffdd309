"use client";
import { Contact } from "lucide-react";
import { useState } from "react";
import {
  FaBolt,
  FaLightbulb,
  FaCamera,
  FaTools,
  FaClock,
} from "react-icons/fa";
import { GiCampingTent } from "react-icons/gi";

const rentalsData = [
  {
    id: 1,
    name: "Generator",
    image: "/generator.webp",
    icon: <FaBolt />,
    description: "Reliable power backup for your events and functions.",
    Contact: "Call us for price details",
  },
  {
    id: 2,
    name: "Tent",
    image: "/Tent-img.jpg",
    icon: <GiCampingTent className="text-green-600 text-5xl mx-auto mb-3" />,
    description: "Durable and spacious tents for weddings",
    Contact: "Call us for price details",
  },
  {
    id: 3,
    name: "Lighting System",
    image: "/Lighting Decoration.jpg",
    icon: <FaLightbulb />,
    description: "Brighten up your events with premium lighting setups.",
    Contact: "Call us for price details",
  },
  {
    id: 4,
    name: "DSLR Camera",
    image: "/Dslr.avif",
    icon: <FaCamera />,
    description: "Capture every moment with high-quality DSLR cameras.",
    Contact: "Call us for price details",
  },
];

export default function Rentals() {
  const [rentals, setRentals] = useState(rentalsData);

  return (
    <div className="min-h-screen bg-gray-100 pt-10">
      {/* Page Heading */}
      <div className="text-center py-16 bg-blue-600 text-white">
        <h1 className="text-5xl font-bold drop-shadow-lg">Available Rentals</h1>
        <p className="text-lg mt-2">
          Explore our top-quality rental services for all your event needs.
        </p>
      </div>

      {/* Rental Items */}
      <div className="max-w-6xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 p-6">
        {rentals.map((item) => (
          <div
            key={item.id}
            className="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition"
          >
            <div className="text-5xl text-blue-500 mx-auto mb-4">
              {item.icon}
            </div>
            <img
              src={item.image}
              alt={item.name}
              className="w-full h-40 object-cover rounded-md"
            />
            <h2 className="text-2xl font-semibold mt-4">{item.name}</h2>
            <p className="text-gray-600">{item.description}</p>
            <p className="text-xl font-bold mt-2 text-green-600">
              {item.price}
            </p>
            <button className="mt-4 bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 transition">
              Rent Now
            </button>
          </div>
        ))}
      </div>

      {/* Our New Services Section */}
      <section className="bg-white py-16 text-center">
        <h2 className="text-4xl font-bold text-gray-800">Our New Services</h2>
        <p className="text-lg text-gray-600 mt-2">
          We are expanding our rental services to provide more convenience.
        </p>
        <div className="mt-8 flex flex-col md:flex-row justify-center gap-6">
          <div className="bg-gray-100 p-6 rounded-lg shadow-md max-w-md">
            <FaTools className="text-5xl text-green-600 mx-auto mb-4" />
            <h3 className="text-2xl font-semibold">Event Setup & Decoration</h3>
            <p className="text-gray-600 mt-2">
              Professional event setup and decoration services to make your
              event stand out.
            </p>
            <button className="mt-4 bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600 transition">
              Learn More
            </button>
          </div>
          <div className="bg-gray-100 p-6 rounded-lg shadow-md max-w-md">
            <FaCamera className="text-5xl text-blue-600 mx-auto mb-4" />
            <h3 className="text-2xl font-semibold">
              Photography & Videography
            </h3>
            <p className="text-gray-600 mt-2">
              Capture every special moment with our professional photography
              services.
            </p>
            <button className="mt-4 bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 transition">
              Explore
            </button>
          </div>
        </div>
      </section>

      {/* Upcoming Rentals Section */}
      <section className="bg-gray-200 py-16 text-center">
        <h2 className="text-4xl font-bold text-gray-800">Upcoming Rentals</h2>
        <p className="text-lg text-gray-600 mt-2">
          New rental equipment arriving soon!
        </p>
        <div className="mt-8 flex flex-col md:flex-row justify-center gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md max-w-md">
            <FaClock className="text-5xl text-yellow-600 mx-auto mb-4" />
            <h3 className="text-2xl font-semibold">Luxury Chairs & Tables</h3>
            <p className="text-gray-600 mt-2">
              Elegant seating solutions for your grand events.
            </p>
            <button className="mt-4 bg-yellow-500 text-white px-6 py-2 rounded hover:bg-yellow-600 transition">
              Coming Soon
            </button>
          </div>
          {/* <div className="bg-white p-6 rounded-lg shadow-md max-w-md">
            <FaBolt className="text-5xl text-red-600 mx-auto mb-4" />
            <h3 className="text-2xl font-semibold">High-Power Generators</h3>
            <p className="text-gray-600 mt-2">
              Powerful and efficient generators for large events
            </p>
            <button className="mt-4 bg-red-500 text-white px-6 py-2 rounded hover:bg-red-600 transition">
              Coming Soon
            </button>
          </div> */}
        </div>
      </section>
    </div>
  );
}
// payments 
"use client";
import { useState } from "react";
import { FaCopy, FaCheckCircle, FaArrowLeft } from "react-icons/fa";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function PaymentDetails() {
  const [isCopied1, setIsCopied1] = useState(false);
  const [isCopied2, setIsCopied2] = useState(false);

  const upiId1 = "8082815863@paytm";
  const upiId2 = "8082815863@ybl";

  const handleCopy = (upiId, setIsCopied) => {
    navigator.clipboard.writeText(upiId);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
    toast.success("UPI ID copied!");
  };

  const handleWhatsAppSubmit = () => {
    const whatsappMessage = "Hi, I want to send the payment screenshot.";
    const encodedMessage = encodeURIComponent(whatsappMessage);
    const url = `https://api.whatsapp.com/send?phone=918082815863&text=${encodedMessage}`;
    window.open(url, "_blank");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[url('/background.jpg')] bg-cover bg-center relative">
      <div className="absolute inset-0 bg-black/50 backdrop-blur-md"></div>

      <div className="relative p-4 sm:p-6 rounded-xl shadow-lg w-full max-w-md bg-white/10 backdrop-blur-md border border-white/20 text-white mt-16">
        {/* Back Button */}
        <div
          className="flex items-center space-x-2 mb-4 cursor-pointer text-white/80 hover:text-white"
          onClick={() => window.history.back()}
        >
          <FaArrowLeft className="text-lg" />
          <span className="text-md">Back</span>
        </div>

        <h2 className="text-xl font-semibold text-center mb-4">
          Payment Details
        </h2>

        <p className="text-center text-white/90 text-sm mb-3">
          Scan the QR code or use the UPI ID below:
        </p>
        <img
          src="/QR.jpg"
          alt="QR Code"
          className="w-32 h-32 mx-auto rounded-lg shadow-md"
        />

        {/* UPI IDs */}
        <div className="space-y-2 mt-3">
          {[
            { id: upiId1, setCopied: setIsCopied1, copied: isCopied1 },
            { id: upiId2, setCopied: setIsCopied2, copied: isCopied2 },
          ].map((upi, index) => (
            <div
              key={index}
              className="flex items-center justify-between bg-white/20 p-2 rounded-lg"
            >
              <p className="text-white text-sm">{upi.id}</p>
              <button
                onClick={() => handleCopy(upi.id, upi.setCopied)}
                className="text-green-300 hover:text-green-400"
              >
                {upi.copied ? <FaCheckCircle /> : <FaCopy />}
              </button>
            </div>
          ))}
        </div>

        {/* WhatsApp Button */}
        <button
          onClick={handleWhatsAppSubmit}
          className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 rounded-lg mt-4 transition-all"
        >
          Send Payment Screenshot on WhatsApp
        </button>
      </div>

      {/* Toast Notifications */}
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
      />
    </div>
  );
}




// Navbar code is here 
"use client";
import { useState } from "react";
import Link from "next/link";
import { Menu, X, ChevronDown, Settings } from "lucide-react";
import { FaKey } from "react-icons/fa";

export default function Navbar() {
  const [menuOpen, setMenuOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const isAdmin = true; // Dummy admin check

  const handleLinkClick = () => {
    setMenuOpen(false); // Close menu when a link is clicked
  };

  return (
    <nav className="bg-gradient-to-r from-green-500 to-teal-500 text-white fixed w-full shadow-lg z-50">
      <div className="container mx-auto flex justify-between px-6 py-4 items-center">
        {/* Logo Section */}
        <div className="flex items-center justify-center gap-2">
          <Link className="flex items-center justify-center gap-1" href="/">
            <FaKey
              size={25}
              className="transition-all transform hover:scale-110"
            />
            <h1 className="text-3xl font-extrabold text-white hover:text-green-100 transition-colors duration-300">
              MirRenTX
            </h1>
          </Link>
        </div>

        {/* Desktop Navigation Links */}
        <div className="hidden md:flex space-x-8">
          <Link
            href="/"
            className="hover:text-green-100 transition-colors duration-300"
          >
            Home
          </Link>
          <Link
            href="/rentals"
            className="hover:text-green-100 transition-colors duration-300"
          >
            Rentals
          </Link>

          {/* Services Dropdown */}
          <div className="relative">
            <button
              onClick={() => setDropdownOpen(!dropdownOpen)}
              className="flex items-center hover:text-green-100 transition-colors duration-300"
            >
              Services <ChevronDown className="ml-1" size={16} />
            </button>
            {dropdownOpen && (
              <div className="absolute left-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg transition-all duration-300">
                <Link
                  href="/services"
                  className="block px-4 py-2 hover:bg-gray-700 transition-all duration-300"
                  onClick={() => setDropdownOpen(false)}
                >
                  Services
                </Link>
                <Link
                  href="/WebDevelopment"
                  className="block px-4 py-2 hover:bg-gray-700 transition-all duration-300"
                  onClick={() => setDropdownOpen(false)}
                >
                  Web Development
                </Link>
              </div>
            )}
          </div>

          <Link
            href="/About"
            className="hover:text-green-100 transition-colors duration-300"
          >
            About Us
          </Link>
          <Link
            href="/Contact"
            className="hover:text-green-100 transition-colors duration-300"
          >
            Contact Us
          </Link>
          <Link
            href="/Gallery"
            className="hover:text-green-100 transition-colors duration-300"
          >
            Gallery
          </Link>
          <Link
            href="/Feedback"
            className="hover:text-green-100 transition-colors duration-300"
          >
            Feedback
          </Link>
        </div>

        {/* Login & Admin Settings Section */}
        <div className="hidden md:flex items-center gap-4">
          <button
            type="button"
            className="focus:outline-none text-white font-semibold bg-green-600 hover:bg-green-700 shadow-lg focus:ring-4 focus:ring-green-300 rounded-lg text-sm px-5 py-2.5 transition-all duration-300"
          >
            Sign In
          </button>
          <button
            type="button"
            className="focus:outline-none text-white font-semibold bg-blue-600 hover:bg-blue-700 shadow-lg focus:ring-4 focus:ring-blue-300 rounded-lg text-sm px-5 py-2.5 transition-all duration-300"
          >
            Sign Up
          </button>
          {isAdmin && (
            <Link
              href="/admin"
              className="text-white hover:text-green-100 transition-colors duration-300"
            >
              <Settings size={24} />
            </Link>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button
          onClick={() => setMenuOpen(!menuOpen)}
          className="md:hidden text-white"
        >
          {menuOpen ? <X size={35} /> : <Menu size={35} />}
        </button>
      </div>

      {/* Mobile Navigation Menu */}
      {menuOpen && (
        <div className="md:hidden absolute top-16 left-0 w-full bg-gray-900 shadow-lg p-6 flex flex-col space-y-6">
          <Link
            href="/"
            className="block hover:text-green-100 transition-colors duration-300"
            onClick={handleLinkClick}
          >
            Home
          </Link>
          <Link
            href="/rentals"
            className="block hover:text-green-100 transition-colors duration-300"
            onClick={handleLinkClick}
          >
            Rentals
          </Link>
          <Link
            href="/services"
            className="block hover:text-green-100 transition-colors duration-300"
            onClick={handleLinkClick}
          >
            Services
          </Link>
          <Link
            href="/WebDevelopment"
            className="block hover:text-green-100 transition-colors duration-300"
            onClick={handleLinkClick}
          >
            Web Development
          </Link>
          <Link
            href="/About"
            className="block hover:text-green-100 transition-colors duration-300"
            onClick={handleLinkClick}
          >
            About Us
          </Link>
          <Link
            href="/Contact"
            className="block hover:text-green-100 transition-colors duration-300"
            onClick={handleLinkClick}
          >
            Contact Us
          </Link>
          <Link
            href="/Gallery"
            className="block hover:text-green-100 transition-colors duration-300"
            onClick={handleLinkClick}
          >
            Gallery
          </Link>
          <Link
            href="/Feedback"
            className="block hover:text-green-100 transition-colors duration-300"
            onClick={handleLinkClick}
          >
            Feedback
          </Link>
          <div className="flex flex-col space-y-3">
            <button
              type="button"
              className="w-full text-white font-semibold bg-green-500 hover:bg-green-600 focus:ring-4 focus:ring-green-300 rounded-lg text-sm px-5 py-2.5 transition-all duration-300"
            >
              Sign In
            </button>
            <button
              type="button"
              className="w-full text-white font-semibold bg-blue-500 hover:bg-blue-600 focus:ring-4 focus:ring-blue-300 rounded-lg text-sm px-5 py-2.5 transition-all duration-300"
            >
              Sign Up
            </button>
          </div>
          {isAdmin && (
            <Link
              href="/admin"
              className="block text-white hover:text-green-100 transition-colors duration-300"
              onClick={handleLinkClick}
            >
              <Settings size={24} />
            </Link>
          )}
        </div>
      )}
    </nav>
  );
}
// Feedback 
"use client";

import { useState } from "react";
import { toast } from "react-hot-toast";
import { FaStar } from "react-icons/fa";
import ProtectedRoute from "../components/ProtectedRoute";

export default function FeedbackPage() {
  const [form, setForm] = useState({ name: "", email: "", feedback: "" });
  const [loading, setLoading] = useState(false);
  const [rating, setRating] = useState(null);
  const [showMessage, setShowMessage] = useState(false);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!form.name || !form.email || !form.feedback) {
      toast.error("All fields are required.");
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
      toast.error("Invalid email address.");
      return;
    }

    if (form.feedback.length > 300) {
      toast.error("Feedback must be under 300 characters.");
      return;
    }

    setLoading(true);

    const response = await fetch("/api/feedback", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(form),
    });

    const data = await response.json();
    setLoading(false);

    if (data.success) {
      toast.success(data.message);
      setForm({ name: "", email: "", feedback: "" });
    } else {
      toast.error(data.message);
    }
  };

  const handleStarClick = async (starValue) => {
    setRating(starValue);
    setShowMessage(true);

    const starFeedback = {
      1: "😢 We're sorry you had a bad experience.",
      2: "🙁 We appreciate your feedback.",
      3: "😐 Thanks for your input!",
      4: "😊 Glad you liked it!",
      5: "🤩 Thank you for your amazing feedback!",
    };

    toast.success(starFeedback[starValue]);

    await fetch("/api/sendStarFeedback", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ rating: starValue }),
    });
  };

  return (
    <ProtectedRoute>
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-blue-100 to-white p-6">
        <section className="max-w-3xl mx-auto text-center mt-28 mb-12">
          <h1 className="text-4xl font-bold text-gray-900">
            We Appreciate Your Feedback
          </h1>
          <p className="text-lg text-gray-600 mt-4">
            Help us improve by sharing your experience with our services.
          </p>
        </section>

        {!showMessage ? (
          <div className="flex justify-center gap-2 text-yellow-400 text-3xl mb-4">
            {[1, 2, 3, 4, 5].map((star) => (
              <FaStar
                key={star}
                className="cursor-pointer"
                onClick={() => handleStarClick(star)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center text-2xl font-semibold mt-4">
            {rating === 1 && "😢 We're sorry you had a bad experience."}
            {rating === 2 && "🙁 We appreciate your feedback."}
            {rating === 3 && "😐 Thanks for your input!"}
            {rating === 4 && "😊 Glad you liked it!"}
            {rating === 5 && "🤩 Thank you for your amazing feedback!"}
          </div>
        )}

        <div className="bg-white shadow-2xl rounded-2xl p-8 max-w-lg w-full border border-gray-200">
          <h2 className="text-3xl font-bold mb-6 text-center text-gray-800">
            Share Your Feedback
          </h2>
          <form onSubmit={handleSubmit} className="space-y-5">
            <input
              type="text"
              name="name"
              placeholder="Your Name"
              value={form.name}
              onChange={handleChange}
              className="w-full text-gray-800 p-3 border rounded-lg shadow-sm focus:ring-2 focus:ring-indigo-400"
              required
            />
            <input
              type="email"
              name="email"
              placeholder="Your Email"
              value={form.email}
              onChange={handleChange}
              className="w-full p-3 text-gray-800 border rounded-lg shadow-sm focus:ring-2 focus:ring-indigo-400"
              required
            />
            <textarea
              name="feedback"
              placeholder="Your Feedback (Max 300 characters)"
              value={form.feedback}
              onChange={handleChange}
              maxLength="300"
              className="w-full p-3 text-gray-800 border rounded-lg shadow-sm focus:ring-2 focus:ring-indigo-400"
              required
            />
            <p className="text-gray-500 text-sm">Max 300 characters allowed.</p>
            <p className="text-gray-600 text-center font-semibold text-sm">
              Kindly take a moment to tell us what you think
            </p>
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-indigo-600 text-white p-3 rounded-lg hover:bg-indigo-700 transition font-semibold shadow-md"
            >
              {loading ? "Submitting..." : "Share My Feedback"}
            </button>
          </form>
        </div>
      </div>
    </ProtectedRoute>
  );
}
